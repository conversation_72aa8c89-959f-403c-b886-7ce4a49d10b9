import { authApi } from "@/services/api";
import { useSettingStore } from "@/store/settingStore";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useEffect, useMemo, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

export default function GroupsScreen() {
  const router = useRouter();
  const colors = useSettingStore((state) => state.colors);
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);

  const styles = createGroupsStyles(colors);

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      setLoading(true);
      const data = await authApi.getSubnetGroups();
      setGroups(data || []);
    } catch (error) {
      console.error("Error loading groups:", error);
      Alert.alert("Error", "Failed to load groups. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadGroups();
    setRefreshing(false);
  };

  // Filter groups based on search query
  const filteredGroups = useMemo(() => {
    if (!searchQuery.trim()) {
      return groups;
    }

    const query = searchQuery.toLowerCase();
    return groups.filter(
      (group) =>
        group.name.toLowerCase().includes(query) ||
        group.relmName.toLowerCase().includes(query) ||
        group.regionName.toLowerCase().includes(query) ||
        group.zoneName.toLowerCase().includes(query) ||
        group.subnetName.toLowerCase().includes(query) ||
        (group.comment && group.comment.toLowerCase().includes(query))
    );
  }, [groups, searchQuery]);

  const toggleSearch = () => {
    setShowSearch(!showSearch);
    if (showSearch) {
      setSearchQuery("");
    }
  };

  const navigateToDevices = (group) => {
    router.push({
      pathname: "/devices",
      params: {
        groupId: group.id,
        groupName: group.name,
      },
    });
  };

  const renderGroupItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => navigateToDevices(item)}
      activeOpacity={0.7}
    >
      <LinearGradient
        colors={colors.gradients.surface}
        style={styles.groupCard}
      >
        <View style={styles.groupHeader}>
          <View style={styles.groupIcon}>
            <Ionicons
              name={item.id === "all" ? "globe-outline" : "business-outline"}
              size={24}
              color={colors.primary}
            />
          </View>
          <View style={styles.groupInfo}>
            <Text style={styles.groupName}>{item.name}</Text>
            <Text style={styles.groupSubtitle}>
              {item.relmName} • {item.deviceCount} devices
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textMuted} />
        </View>

        {item.regionName && (
          <View style={styles.groupDetails}>
            <View style={styles.detailRow}>
              <Ionicons name="map-outline" size={16} color={colors.textMuted} />
              <Text style={styles.detailText}>{item.regionName}</Text>
            </View>
            {item.zoneName && (
              <View style={styles.detailRow}>
                <Ionicons
                  name="location-outline"
                  size={16}
                  color={colors.textMuted}
                />
                <Text style={styles.detailText}>{item.zoneName}</Text>
              </View>
            )}
            {item.subnetName && (
              <View style={styles.detailRow}>
                <Ionicons
                  name="git-network"
                  size={16}
                  color={colors.textMuted}
                />
                <Text style={styles.detailText}>{item.subnetName}</Text>
              </View>
            )}
          </View>
        )}

        {item.comment && (
          <Text style={styles.groupComment}>{item.comment}</Text>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <LinearGradient
        colors={colors.gradients.background}
        style={styles.centerContainer}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading groups...</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={colors.gradients.background}
      style={styles.container}
    >
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={styles.headerContent}>
            <Text style={styles.title}>Device Groups</Text>
            <Text style={styles.subtitle}>
              {filteredGroups.length} of {groups.length} group
              {groups.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <View style={styles.headerButtons}>
            <TouchableOpacity onPress={toggleSearch}>
              <LinearGradient
                colors={colors.gradients.surface}
                style={styles.searchButton}
              >
                <Ionicons
                  name={showSearch ? "close" : "search"}
                  size={20}
                  color={colors.text}
                />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>

        {showSearch && (
          <LinearGradient
            colors={colors.gradients.surface}
            style={styles.searchContainer}
          >
            <Ionicons name="search" size={20} color={colors.textMuted} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search groups..."
              placeholderTextColor={colors.textMuted}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus={true}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={colors.textMuted}
                />
              </TouchableOpacity>
            )}
          </LinearGradient>
        )}
      </View>

      <FlatList
        data={filteredGroups}
        renderItem={renderGroupItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons name="search" size={64} color={colors.textMuted} />
            <Text style={styles.emptyTitle}>No Groups Found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery
                ? `No groups match "${searchQuery}"`
                : "No groups available"}
            </Text>
          </View>
        )}
      />
    </LinearGradient>
  );
}

const createGroupsStyles = (colors) => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    centerContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.textMuted,
    },
    header: {
      paddingHorizontal: 20,
      paddingTop: 10,
      paddingBottom: 16,
    },
    headerTop: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
    },
    headerContent: {
      flex: 1,
    },
    headerButtons: {
      flexDirection: "row",
      alignItems: "center",
    },
    title: {
      fontSize: 28,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textMuted,
    },
    searchButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: colors.surface,
      justifyContent: "center",
      alignItems: "center",
      marginLeft: 16,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.surface,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginTop: 16,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,
      color: colors.text,
      marginLeft: 12,
      marginRight: 12,
    },
    listContainer: {
      paddingHorizontal: 20,
      paddingBottom: 100, // Space for tab bar
    },
    groupCard: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      padding: 20,
      marginBottom: 16,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    groupHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    groupIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: `${colors.primary}15`,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 16,
    },
    groupInfo: {
      flex: 1,
    },
    groupName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
      marginBottom: 4,
    },
    groupSubtitle: {
      fontSize: 14,
      color: colors.textMuted,
    },
    groupDetails: {
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    detailText: {
      fontSize: 14,
      color: colors.textMuted,
      marginLeft: 8,
    },
    groupComment: {
      fontSize: 14,
      color: colors.textMuted,
      fontStyle: "italic",
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingTop: 100,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: "600",
      color: colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptySubtitle: {
      fontSize: 16,
      color: colors.textMuted,
      textAlign: "center",
    },
  });
};
