import useAuthOperations from "@/services/useAuth";
import { useSettingStore } from "@/store/settingStore";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  Button,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
} from "react-native";

const LoginScreen = () => {
  const colors = useSettingStore((state) => state.colors);
  const isDarkMode = useSettingStore((state) => state.isDarkMode);
  const router = useRouter();
  const { login, isLoading } = useAuthOperations();

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState({});

  const authStyles = createAuthStyles(colors);

  const validateForm = () => {
    let errors = {};

    if (!username) errors.username = "Username is required";
    if (!password) errors.password = "Password is required";

    setErrors(errors);

    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      console.log("Submitted", username, password);
      try {
        const result = await login({
          user: username.trim(),
          password: password.trim(),
        });

        if (result.requiresTwoFA) {
          // User has 2FA enabled, navigate to 2FA screen
          router.push("/(auth)/2fa-verification");
        } else {
          // Direct login without 2FA, navigate to main app
          router.replace("/(tabs)");
        }
      } catch (error) {
        console.error("Login error:", error);
        Alert.alert("Login failed", error.message);
      } finally {
        setUsername("");
        setPassword("");
        setErrors({});
      }
    }
  };

  return (
    <LinearGradient colors={colors.gradients.background} style={{ flex: 1 }}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 40}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: "center",
            paddingHorizontal: 24,
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <LinearGradient
            colors={colors.gradients.surface}
            style={authStyles.form}
          >
            <Image
              source={
                isDarkMode
                  ? require("@/assets/images/darkmode-logo.svg")
                  : require("@/assets/images/lightmode-ogo.svg")
              }
              style={{
                width: 216,
                height: 90,
                alignSelf: "center",
                marginBottom: 30,
              }}
              contentFit="cover"
            />
            <Text style={authStyles.titleText}>WELCOME BACK</Text>
            <Text style={authStyles.label}>Username</Text>
            <TextInput
              style={authStyles.input}
              placeholder="Enter your username"
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              autoCorrect={false}
              placeholderTextColor={colors.textMuted}
            />
            {errors.username ? (
              <Text style={authStyles.errorText}>{errors.username}</Text>
            ) : null}

            <Text style={authStyles.label}>Password</Text>
            <TextInput
              style={authStyles.input}
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
              placeholderTextColor={colors.textMuted}
            />
            {errors.password ? (
              <Text style={authStyles.errorText}>{errors.password}</Text>
            ) : null}

            <Button
              title="Login"
              onPress={handleSubmit}
              disabled={isLoading}
              color={colors.primary}
            />
          </LinearGradient>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

export default LoginScreen;

const createAuthStyles = (colors) => {
  const styles = StyleSheet.create({
    form: {
      backgroundColor: "#ffffff",
      padding: 20,
      borderRadius: 10,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    label: {
      fontSize: 16,
      marginBottom: 5,
      fontWeight: "bold",
      color: colors.text,
    },
    input: {
      height: 40,
      backgroundColor: colors.backgrounds.input,
      borderColor: colors.border,
      color: colors.text,
      borderWidth: 1,
      marginBottom: 15,
      padding: 10,
      borderRadius: 5,
    },
    errorText: {
      color: "red",
      marginBottom: 10,
    },
    button: {
      backgroundColor: colors.primary,
      padding: 10,
      borderRadius: 5,
      alignItems: "center",
    },
    buttonText: {
      color: "#ffffff",
      fontSize: 16,
      fontWeight: "bold",
    },
    titleText: {
      fontSize: 20,
      fontWeight: "bold",
      color: colors.text,
      textAlign: "center",
      marginBottom: 20,
    },
  });

  return styles;
};
