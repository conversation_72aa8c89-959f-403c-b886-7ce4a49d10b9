import { useAuth } from "@/services/authStore";
import useAuthOperations from "@/services/useAuth";
import { useSettingStore } from "@/store/settingStore";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  Button,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";

const TwoFaVerification = () => {
  const colors = useSettingStore((state) => state.colors);
  const isDarkMode = useSettingStore((state) => state.isDarkMode);
  const authStyles = createAuthStyles(colors);
  const router = useRouter();
  const { sessionID, clearAuthData } = useAuth();
  const { validate2FA, isLoading } = useAuthOperations();

  const [code, setCode] = useState("");
  const [error, setError] = useState("");

  const handleVerify2FA = async () => {
    if (!code.trim()) {
      setError("Please enter the 2FA code");
      return;
    }

    if (code.length !== 6) {
      setError("2FA code must be 6 digits");
      return;
    }

    setError("");

    try {
      await validate2FA(sessionID, code.trim());
      // Navigate to main app on success
      router.replace("/(tabs)");
    } catch (error) {
      setError("Invalid 2FA code. Please try again.");
    }
  };

  const handleCancel = async () => {
    Alert.alert(
      "Cancel Authentication",
      "Are you sure you want to cancel? You'll need to sign in again.",
      [
        {
          text: "No",
          style: "cancel",
        },
        {
          text: "Yes",
          style: "destructive",
          onPress: async () => {
            await clearAuthData();
            router.replace("/(auth)/sign-in");
          },
        },
      ]
    );
  };

  const formatCode = (text) => {
    // Remove non-numeric characters and limit to 6 digits
    const numericText = text.replace(/[^0-9]/g, "").slice(0, 6);
    return numericText;
  };

  return (
    <LinearGradient colors={colors.gradients.background} style={{ flex: 1 }}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 40}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: "center",
            paddingHorizontal: 24,
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <LinearGradient
            colors={colors.gradients.surface}
            style={authStyles.form}
          >
            <Image
              source={
                isDarkMode
                  ? require("@/assets/images/darkmode-logo.svg")
                  : require("@/assets/images/lightmode-ogo.svg")
              }
              style={{
                width: 216,
                height: 90,
                alignSelf: "center",
                marginBottom: 30,
              }}
              contentFit="cover"
            />
            <Text style={authStyles.titleText}>Two-Factor Authentication</Text>
            <Text style={authStyles.subtitle}>
              Open Google Authenticator to get your 2FA code
            </Text>
            <TextInput
              style={authStyles.input}
              placeholder="000000"
              value={code}
              onChangeText={(text) => setCode(formatCode(text))}
              autoCapitalize="none"
              autoCorrect={false}
              placeholderTextColor={colors.textMuted}
              keyboardType="numeric"
              maxLength={6}
              autoFocus
            />

            {error && <Text style={authStyles.errorText}>{error}</Text>}

            <View style={authStyles.buttonContainer}>
              <Button
                title="Verify Code"
                onPress={handleVerify2FA}
                disabled={isLoading || code.length !== 6}
                color={colors.primary}
              />
              <Button
                title="Cancel"
                onPress={handleCancel}
                disabled={isLoading}
                color={colors.textMuted}
              />
            </View>
          </LinearGradient>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

export default TwoFaVerification;

const createAuthStyles = (colors) => {
  const styles = StyleSheet.create({
    form: {
      backgroundColor: "#ffffff",
      padding: 20,
      borderRadius: 10,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    label: {
      fontSize: 16,
      marginBottom: 5,
      fontWeight: "bold",
      color: colors.text,
    },
    input: {
      height: 40,
      backgroundColor: colors.backgrounds.input,
      borderColor: colors.border,
      color: colors.text,
      borderWidth: 1,
      marginBottom: 15,
      padding: 10,
      borderRadius: 5,
      textAlign: "center",
    },
    errorText: {
      color: "red",
      marginBottom: 10,
    },
    button: {
      backgroundColor: colors.primary,
      padding: 10,
      borderRadius: 5,
      alignItems: "center",
    },
    buttonText: {
      color: "#ffffff",
      fontSize: 16,
      fontWeight: "bold",
    },
    titleText: {
      fontSize: 20,
      fontWeight: "bold",
      color: colors.text,
      textAlign: "center",
      marginBottom: 20,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textMuted,
      textAlign: "center",
      marginBottom: 20,
    },
    buttonContainer: {
      gap: 15,
    },
  });

  return styles;
};
